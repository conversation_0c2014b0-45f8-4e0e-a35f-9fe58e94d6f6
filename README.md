# Elysia with Bun runtime

## Getting Started
To get started with this template, simply paste this command into your terminal:
```bash
bun create elysia ./elysia-example
```

## Development
To start the development server run:
```bash
bun run dev
```

Open http://localhost:3000/ with your browser to see the result.

## Command
## run docker compose container and stop
$ docker compose up --build -d
$ docker compose down

## run prisma db push for run migration
$ docker exec -it linkhub-api bunx prisma db push