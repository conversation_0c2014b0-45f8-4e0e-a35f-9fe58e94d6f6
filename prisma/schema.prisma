// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model users {
  id            String   @id @default(cuid()) @db.VarChar(36)
  username      String   @unique
  email         String   @unique
  password_hash String
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  landingProfileData        LandingProfileData[]
  resetTokens               PasswordResetToken[]
  projects                  Project[]
  landingTestimonials       LandingTestimonial[]
  landingContactSubmissions LandingContactSubmission[]
  landingServices           LandingService[]
}

model profiles {
  user_id      String   @unique
  display_name String?
  bio          String?
  avatar_url   String?
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
}

model links {
  id            String   @id @default(cuid())
  user_id       String
  label         String
  url           String
  icon          String?
  display_order Int      @default(0)
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt
}

model PasswordResetToken {
  id        String    @id @default(cuid())
  userId    String
  tokenHash String    @unique
  expiresAt DateTime
  usedAt    DateTime?
  createdAt DateTime  @default(now())

  user users @relation(fields: [userId], references: [id])
  @@index([userId])
  @@index([expiresAt])
}

model LandingProfileData {
  landing_page_id String   @id @unique @default(cuid())
  user_id         String   @db.VarChar(36)
  name            String
  title           String
  bio             String?  @db.Text
  about           String?  @db.Text
  journey         String?  @db.Text
  email           String
  phone           String
  location        String
  github          String
  linkedin        String
  twitter         String
  image           String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  user users @relation(fields: [user_id], references: [id])

  @@index([landing_page_id])
  @@index([user_id])
}

model LandingSkillCategory {
  landing_page_id String   @unique @db.VarChar(36)
  category_id     String
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  skill_category SkillCategorys @relation(fields: [category_id], references: [id])

  @@index([landing_page_id])
  @@index([category_id])
}

model LandingSkill {
  id              String   @id @default(cuid()) @db.VarChar(36)
  landing_page_id String   @db.VarChar(36)
  category_id     String   @db.VarChar(36)
  skill_id        String   @db.VarChar(36)
  level           Int
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  skill Skill @relation(fields: [skill_id], references: [id])

  @@index([landing_page_id])
  @@index([category_id])
  @@index([skill_id])
}

model SkillCategorys {
  id         String   @id @default(cuid()) @db.VarChar(36)
  title      String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  skill_category LandingSkillCategory[]

  @@index([id])
}

model Skill {
  id          String   @id @default(cuid()) @db.VarChar(36)
  category_id String   @db.VarChar(36)
  name        String
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  skill LandingSkill[]

  @@index([id])
  @@index([category_id])
}

// Project model - stores project section information
model Project {
  id          String   @id @default(cuid()) @db.VarChar(36)
  user_id     String   @db.VarChar(36)
  title       String   @db.VarChar(255)
  description String   @db.Text
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)

  projectCards ProjectCard[]

  @@index([user_id], map: "fk_projects_users_idx")
  @@map("projects")
}

// ProjectCard model - stores individual project cards
model ProjectCard {
  id          String   @id @default(cuid()) @db.VarChar(36)
  project_id  String   @db.VarChar(36)
  no          Int      @default(0) // Order number for the card
  title       String   @db.VarChar(255)
  description String   @db.Text
  image       String   @db.VarChar(2048)
  liveUrl     String?  @map("live_url") @db.VarChar(2048)
  githubUrl   String?  @map("github_url") @db.VarChar(2048)
  tags        String?  @db.Text // JSON string of tags array
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  project Project @relation(fields: [project_id], references: [id], onDelete: Cascade)

  @@index([project_id], map: "fk_project_cards_projects_idx")
  @@index([no], map: "idx_project_cards_order")
  @@map("project_cards")
}

// Landing page testimonials
model LandingTestimonial {
  id           String   @id @default(cuid()) @db.VarChar(36)
  user_id      String   @db.VarChar(36)
  client_name  String   @db.VarChar(255)
  client_title String?  @db.VarChar(255)
  client_image String?  @db.VarChar(2048)
  testimonial  String   @db.Text
  rating       Int?     @default(5) // 1-5 star rating
  displayOrder Int      @default(0) @map("display_order")
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], map: "fk_testimonials_users_idx")
  @@index([displayOrder], map: "idx_testimonials_order")
  @@map("landing_testimonials")
}

// Landing page contact form submissions
model LandingContactSubmission {
  id        String   @id @default(cuid()) @db.VarChar(36)
  user_id   String   @db.VarChar(36)
  name      String   @db.VarChar(255)
  email     String   @db.VarChar(255)
  subject   String?  @db.VarChar(255)
  message   String   @db.Text
  isRead    Boolean  @default(false) @map("is_read")
  createdAt DateTime @default(now()) @map("created_at")

  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], map: "fk_contact_submissions_users_idx")
  @@index([isRead], map: "idx_contact_submissions_read")
  @@index([createdAt], map: "idx_contact_submissions_date")
  @@map("landing_contact_submissions")
}

// Landing page services/offerings
model LandingService {
  id           String   @id @default(cuid()) @db.VarChar(36)
  user_id      String   @db.VarChar(36)
  title        String   @db.VarChar(255)
  description  String   @db.Text
  icon         String?  @db.VarChar(255) // Icon name or URL
  price        String?  @db.VarChar(100) // e.g., "$500", "Contact for quote"
  features     String?  @db.Text // JSON string of features array
  displayOrder Int      @default(0) @map("display_order")
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], map: "fk_services_users_idx")
  @@index([displayOrder], map: "idx_services_order")
  @@map("landing_services")
}
