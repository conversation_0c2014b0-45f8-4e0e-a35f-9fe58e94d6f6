import { <PERSON><PERSON>, t } from "elysia";
import { PrismaClient } from "./generated/prisma";
import nodemailer from 'nodemailer';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { cors } from '@elysiajs/cors';
import { staticPlugin } from '@elysiajs/static';

interface User {
  id: string;
  email: string;
  password: string;
  username: string;
  displayName: string;
  bio: string;
  profileImage: string;
  token: string;  
}
interface Profile {
  id: string;
  user_id: string;
  display_name: string;
  bio: string;
  avatar_url: string;
  image: any;
}
interface LandingProfile{
  id: string;
  user_id: string;
  name: string;
  title: string;
  bio: string;
  about: string;
  journey: string;
  email: string;
  phone: string;
  location: string;
  github: string;
  linkedin: string;
  twitter: string;
  image: any;
}

const JWT_SECRET = process.env.JWT_SECRET || 'secret';
const ORIGIN = process.env.ORIGIN || 'http://localhost';
const URL_UPLOAD = process.env.URL_UPLOAD || 'http://localhost:3030/api/upload';
const prisma = new PrismaClient();

// โหลด ENV
const {
  APP_ORIGIN,
  SMTP_HOST,
  SMTP_PORT,
  SMTP_USER,
  SMTP_PASS,
  MAIL_FROM_NAME,
  MAIL_FROM_EMAIL,
  RESET_TOKEN_TTL_MIN
} = process.env

// Nodemailer transporter
const transporter = nodemailer.createTransport({
  host: SMTP_HOST,
  port: Number(SMTP_PORT),
  secure: false,
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASS
  }
});

function sha256(input: string) {
  return crypto.createHash('sha256').update(input).digest('hex')
}
function randomToken(bytes = 32) {
  return crypto.randomBytes(bytes).toString('hex')
}
function addMinutes(date: Date, minutes: number) {
  return new Date(date.getTime() + minutes * 60_000)
}
async function sendResetEmail(to: string, token: string) {
  const resetUrl = `${APP_ORIGIN}/reset-password?token=${encodeURIComponent(token)}`
  const html = `
    <div style="font-family:system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,sans-serif">
      <h2>รีเซ็ตรหัสผ่าน</h2>
      <p>มีการร้องขอให้รีเซ็ตรหัสผ่านสำหรับบัญชีของคุณ</p>
      <p>
        <a href="${resetUrl}" style="display:inline-block;padding:10px 16px;border-radius:8px;background:#0ea5e9;color:#fff;text-decoration:none;font-weight:600">
          คลิกเพื่อรีเซ็ตรหัสผ่าน
        </a>
      </p>
      <p>หากปุ่มกดไม่ได้ ให้คัดลอกลิงก์นี้ไปวางในเบราว์เซอร์:</p>
      <p><code>${resetUrl}</code></p>
      <hr/>
      <p style="color:#64748b">ลิงก์หมดอายุใน ${RESET_TOKEN_TTL_MIN} นาที และใช้ได้ครั้งเดียว</p>
      <p style="color:#64748b">ถ้าคุณไม่ได้ร้องขอ สามารถเพิกเฉยอีเมลนี้ได้</p>
    </div>
  `
  await transporter.sendMail({
    to,
    from: `${MAIL_FROM_NAME} <${MAIL_FROM_EMAIL}>`,
    subject: 'รีเซ็ตรหัสผ่าน',
    html
  })
}

const authMiddleware = (app: Elysia) =>
  app.derive(({ cookie, set }) => {
    const token = cookie.token.value;
    if (token) {
      const profile: any = jwt.verify(token, JWT_SECRET);
      if (!profile) {
        return {};
      }
      const user = prisma.users.findUnique({
        where: { id: profile.id }
      });
      if (!user) {
        return {};
      }
      set.status = 200;
      return { profile };
    } else {
      console.log('No token');
      return {};
    }
  });

const app = new Elysia()
  .use(cors({
    origin: ORIGIN,
    credentials: true
  }))
  .use(staticPlugin({
    prefix: '/uploads',
    assets: './uploads'
  }))

  // 1) ขอรีเซ็ตรหัสผ่าน (ส่งอีเมล)
  .post('/auth/reset/request', ({ body }) => body, {
    body: t.Object({ email: t.String({ format: 'email' }) }),
    async afterHandle({ body, set }) {
      const email = body.email.toLowerCase().trim();
      // หาผู้ใช้ (ไม่เผยแพร่ผลลัพธ์)
      const user = await prisma.users.findUnique({ where: { email } });

      // สร้างโทเค็น กรณีพบผู้ใช้เท่านั้น (แต่ response จะเป็นแบบเดียวกัน)
      if (user) {
        // ล้างโทเค็นเก่าที่หมดอายุ
        await prisma.passwordResetToken.deleteMany({
          where: {
            userId: user.id,
            OR: [
              { expiresAt: { lt: new Date() } },
              { usedAt: { not: null } }
            ]
          }
        })

        const token = randomToken(32) // 64 hex chars
        const tokenHash = sha256(token)
        const expiresAt = addMinutes(new Date(), Number(RESET_TOKEN_TTL_MIN) || 15)

        await prisma.passwordResetToken.create({
          data: { userId: user.id, tokenHash, expiresAt }
        })

        // ส่งอีเมล (ถ้าล้มเหลวไม่ต้องเผยใน response)
        try {
          await sendResetEmail(email, token)
        } catch (e) {
          console.error('send mail error', e)
          // ถ้าส่งเมลล้มเหลว จะยังตอบ 200 เพื่อกันเดาอีเมล
        }
      }

      // ตอบแบบเดียวเสมอ ป้องกัน user enumeration
      set.status = 200
      return { ok: true, message: 'ถ้ามีบัญชีนี้ เราได้ส่งอีเมลรีเซ็ตแล้ว' }
    }
  })

  // 2) ยืนยันโทเค็นและตั้งรหัสผ่านใหม่
  .post('/auth/reset/confirm', ({ body }) => body, {
    body: t.Object({
      token: t.String({ minLength: 10 }), // hex ยาว ~64
      newPassword: t.String({ minLength: 8 }) // กำหนด policy เองได้
    }),
    async afterHandle({ body, set }) {
      const { token, newPassword } = body
      const tokenHash = sha256(token)
      const record = await prisma.passwordResetToken.findUnique({
        where: { tokenHash },
        include: { user: true }
      })

      // ตรวจสอบโทเค็น
      if (!record || record.usedAt || record.expiresAt < new Date()) {
        set.status = 400
        return { ok: false, error: 'invalid_or_expired_token' }
      }

      // อัปเดตรหัสผ่าน
      const passwordHash = await bcrypt.hash(newPassword, 12)
      await prisma.$transaction([
        prisma.users.update({
          where: { id: record.userId },
          data: { password_hash: passwordHash }
        }),
        prisma.passwordResetToken.update({
          where: { tokenHash },
          data: { usedAt: new Date() }
        }),
        // Optional: ยกเลิกโทเค็นอื่นๆ ของผู้ใช้นี้
        prisma.passwordResetToken.deleteMany({
          where: {
            userId: record.userId,
            tokenHash: { not: tokenHash }
          }
        })
      ])

      set.status = 200
      return { ok: true, message: 'ตั้งรหัสผ่านใหม่เรียบร้อย' }
    }
  })

  .get('/api/logout', ({ cookie: { token } }) => {
    token.set({
      value: '',
      httpOnly: true,
      maxAge: 0,
      expires: new Date(0),
      secure: false,
      sameSite: 'strict',
      path: '/'
    })
    return {
      status: true,
      message: "Logout successful"
    };
  })

  .post("/api/login", async ({ body, cookie: { token } }) => {
    try {
      const { email, password } = body as User;
      const user = await prisma.users.findUnique({
        where: { email }
      });
      if (!user) {
        throw new Error('User or password not found');
      }
      const isPasswordValid = await bcrypt.compare(password, user.password_hash);
      if (!isPasswordValid) {
        throw new Error('User or password not found');
      }
      const profile = await prisma.profiles.findUnique({
        where: { user_id: user.id }
      });
      const links = await prisma.links.findMany({
        where: { user_id: user.id },
        orderBy: { display_order: "asc" }
      });
      const token_value = jwt.sign(
        {
          id: user.id,
          username: user.username
        },
        JWT_SECRET,
        { expiresIn: '7d' }
      );

      token.set({
        value: token_value,
        httpOnly: true,
        sameSite: 'strict',     // 'none' ถ้า cross-origin 'strict' ถ้า same-origin
        secure: false,          // ใส่ true ถ้าเป็น HTTPS
        path: '/',
        expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        maxAge: 60 * 60 * 24 * 7  // 7 วัน
      })
      return {
        status: true,
        message: "Login successful",
        user: {
          id: user?.id,
          username: user?.username,
          email: user?.email,
          displayName: profile?.display_name,
          bio: profile?.bio,
          profileImage: profile?.avatar_url
        },
        links: links
      };
    } catch (error: any) {
      console.log('Error:', error.message);
      return {
        status: false,
        message: error.message,
        user: null
      };
    }

  })

  .get('/api/checkuser/:username', async ({ params: { username } }) => {
    const exsitingUser = await prisma.users.findUnique({
      where: { username }
    });
    if (exsitingUser) {
      return { status: false, message: "User already exists" };
    }
    return { status: true, message: "User available" };
  })
  .get('/api/checkemail/:email', async ({ params: { email } }) => {
    const exsitingEmail = await prisma.users.findUnique({
      where: { email }
    });
    if (exsitingEmail) {
      return { status: false, message: "Email already exists" };
    }
    return { status: true, message: "Email available" };
  })

  .post("/api/register", async ({ body, cookie: { token } }) => {
    const { username, email, password } = body as User;
    const hashedPassword = await bcrypt.hash(password, 10);
    try {
      const exsitingUser = await prisma.users.findUnique({
        where: { username }
      });
      if (exsitingUser) {
        //throw new Error('User already exists'); 
        return { status: false, message: "User already exists" };
      }
      const exsitingEmail = await prisma.users.findUnique({
        where: { email }
      });
      if (exsitingEmail) {
        //throw new Error('Email already exists');
        return { status: false, message: "Email already exists" };
      }
      const user = await prisma.users.create({
        data: {
          username,
          email,
          password_hash: hashedPassword
        }
      });
      const profile = await prisma.profiles.create({
        data: {
          user_id: user.id,
          display_name: user.username
        }
      });
      if (!profile) {
        throw new Error('Invalid profile');
      }

      const token_value = jwt.sign(
        {
          id: user.id,
          username: user.username
        },
        JWT_SECRET,
        { expiresIn: '7d' }
      )

      token.set({
        value: token_value,
        httpOnly: true,
        sameSite: 'strict',   // 'none' ถ้า cross-origin 'strict' ถ้า same-origin
        secure: false,        // ใส่ true ถ้าเป็น HTTPS
        path: '/',
        expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        maxAge: 60 * 60 * 24 * 7    // 7 วัน
      })

      return {
        status: true,
        message: "User created successfully",
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          displayName: profile.display_name,
          bio: profile.bio,
          profileImage: profile.avatar_url
        }
      };

    } catch (error: any) {

      return {
        status: false,
        message: error,
        user: null
      }
    }
  })

  .get('/api/pages/:username', async ({ params }) => {
    const username = params.username;

    if (!username) {
      return { error: `Invalid page : ${username}` }; // ตรวจสอบว่ามีชื่อผู้ใช้นี้หรือไม่
    }
    const user = await prisma.users.findUnique({
      where: { username },
    });
    if (!user) {
      return { error: "User not found" };
    }
    const userProfile = await prisma.profiles.findUnique({
      where: { user_id: user.id }
    });
    const links = await prisma.links.findMany({
      where: { user_id: user.id },
      orderBy: { display_order: "asc" }
    });
    const dataUser = {
      id: user?.id,
      email: user?.email,
      username: user?.username,
      displayName: userProfile?.display_name,
      bio: userProfile?.bio,
      profileImage: userProfile?.avatar_url
    }
    return {
      status: true,
      message: "Links found",
      user: dataUser,
      links: links
    };
  })

  .group("/api/admin", (app) =>
    app
      .use(authMiddleware)
      .get("/profile", async ({ profile }) => {
        try {
          if (!profile) {
            return { error: "Invalid token" };
          }
          const { id } = profile;
          const user: any = await prisma.users.findUnique({
            where: { id: id }
          });
          const userProfile = await prisma.profiles.findUnique({
            where: { user_id: id }
          });

          if (!userProfile) {
            return { error: "User not found" };
          }
          const links = await prisma.links.findMany({
            where: { user_id: id },
            orderBy: { display_order: "asc" }
          });

          return {
            status: true,
            message: "Profile found",
            user: {
              id: user?.id,
              email: user?.email,
              username: user?.username,
              displayName: userProfile.display_name,
              bio: userProfile.bio,
              profileImage: userProfile.avatar_url
            },
            links: links
          };

        } catch (error) {
          console.log('Error:', error);
          return { error: "Invalid token" };
        }

      })

      .post("/links", async ({ profile, body }) => {
        const { id } = profile;
        const { label, url, icon } = body as { label: string, url: string, icon: string };
        if (!id) {
          return { error: "Invalid user id" };
        }
        if (!label) {
          return { error: "Invalid label" };
        }
        if (!url) {
          return { error: "Invalid url" };
        }
        if (!icon) {
          return { error: "Invalid icon" };
        }
        const maxorder = await prisma.links.aggregate({
          where: { user_id: id },
          _max: { display_order: true }
        });
        const link = await prisma.links.create({
          data: {
            user_id: id,
            label,
            url,
            icon,
            display_order: (maxorder._max.display_order ?? 0) + 1
          }
        });
        if (!link) {
          return { error: "Invalid link" };
        }
        return {
          status: true,
          message: "Link created successfully",
          link: link,
        }
      })

      .put('/links/:id', async ({ body }) => {
        const { id, label, url, icon, display_order } = body as { id: string, label: string, url: string, icon: string, display_order: number };
        if (!id) {
          return { error: "Invalid id" };
        }
        if (!label) {
          return { error: "Invalid label" };
        }
        if (!url) {
          return { error: "Invalid url" };
        }
        if (!icon) {
          return { error: "Invalid icon" };
        }
        const link = await prisma.links.update({
          where: { id },
          data: {
            label,
            url,
            icon,
            display_order
          }
        });
        if (!link) {
          return { error: "Invalid link" };
        }
        return {
          status: true,
          message: "Link updated successfully",
          link: link,
        }
      })
      .delete('/links/:id', async ({ params }) => {
        const { id } = params;
        if (!id) {
          return { error: "Invalid id" };
        }
        const link = await prisma.links.delete({
          where: { id }
        });
        if (!link) {
          return { error: "Invalid link" };
        }
        return {
          status: true,
          message: "Link deleted successfully",
          link: link,
        }
      })

      .put('/profile', async ({ profile, body, set }) => {
        try {
          const { display_name, bio, image } = body as Profile;
          const { id } = profile;
          
          if (image) {
            // สร้าง FormData สำหรับส่งต่อไป API ปลายทาง
            const formData = new FormData()
            formData.append('image', image)
            const response = await fetch(URL_UPLOAD, {
              method: 'POST',
              body: formData
            })

            console.log('Response Upload : ', response);

            if (response.status === 200) {
              const data = await response.json()
              console.log('Data : ', data);
              await prisma.profiles.update({
                where: { user_id: id },
                data: { avatar_url: data.filename }
              });
            }
          }

          await prisma.profiles.update({
            where: { user_id: id },
            data: { display_name, bio }
          });

        } catch (error) {
          console.log('Error:', error);
          return { error: "Invalid profile" };
        }
      })
      
      .put('/landing/profile', async ({ profile, body }) => {
        try {
          const  {name, title, bio, about, journey, email, phone, location, github, linkedin, twitter, image } = body as LandingProfile;
          const { id } = profile;
          
          if (image) {
            // สร้าง FormData
          }

          await prisma.landingProfileData.update({
            where: { user_id: id },
            data: { name, title, bio, about, journey, email, phone, location, github, linkedin, twitter }
          });

        } catch (error) {
          console.log('Error:', error);
          return { error: "Invalid profile" };
        }
      })

      .post('/landing/profile', async ({ profile, body }) => {
        try {
          const  {name, title, bio, about, journey, email, phone, location, github, linkedin, twitter, image } = body as LandingProfile;
          const { id } = profile;
          if (image) {
            // สร้าง FormData
          }

          await prisma.landingProfileData.create({            
            data: { user_id: id, name, title, bio, about, journey, email, phone, location, github, linkedin, twitter }
          });

        } catch (error) {
          console.log('Error:', error);
          return { error: "Invalid profile" };
        }
      })

      .get('/landing/profile', async ({ profile }) => {
        try {
          const { id } = profile;
          const pageProfile = await prisma.landingProfileData.findUnique({
            where: { user_id: id }
          });
          
          if (!pageProfile) {
            return{
              status: false,
              message: "Profile not found",
              profile: null
            };
          }
          return {
            status: true,
            message: "Profile found",
            profile: pageProfile
          };
        } catch (error) {
          console.log('Error:', error);
          return { status: false,message: error, profile: null };
        }
      })

  )
  .get('/', () => 'Hello Elysia')
  .listen(3000);

console.log(
  `🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`
);

