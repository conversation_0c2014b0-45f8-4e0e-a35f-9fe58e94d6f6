{"name": "link-hub-api", "version": "1.0.50", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts"}, "dependencies": {"@elysiajs/cookie": "^0.8.0", "@elysiajs/cors": "^1.3.3", "@elysiajs/jwt": "^1.3.2", "@elysiajs/static": "^1.3.0", "@prisma/client": "^6.16.2", "@types/cors": "^2.8.19", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "elysia": "^1.3.20", "elysia-clerk": "^0.12.0", "elysia-connect-middleware": "^0.0.6", "jsonwebtoken": "^9.0.2", "mime": "^1.6.0", "multer": "^2.0.2", "multipart": "^0.1.5", "nodemailer": "^7.0.5"}, "devDependencies": {"@types/nodemailer": "^7.0.1", "bun-types": "latest", "prisma": "^6.16.2"}, "module": "src/index.js"}